{"name": "react-devtools-fusebox", "version": "0.0.0", "private": "true", "license": "MIT", "files": ["dist"], "scripts": {"build:frontend:copy-types": "cp src/*.d.ts dist/", "build:frontend:local": "cross-env NODE_ENV=development webpack --config webpack.config.frontend.js && yarn build:frontend:copy-types", "build:frontend": "cross-env NODE_ENV=production webpack --config webpack.config.frontend.js && yarn build:frontend:copy-types", "build": "yarn build:frontend"}, "devDependencies": {"buffer": "^6.0.3", "cross-env": "^7.0.3", "css-loader": "^6.9.1", "mini-css-extract-plugin": "^2.7.7", "process": "^0.11.10", "webpack": "^5.82.1", "webpack-cli": "^5.1.1", "workerize-loader": "^2.0.2"}}