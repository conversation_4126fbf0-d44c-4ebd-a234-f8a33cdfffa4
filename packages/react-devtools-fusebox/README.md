# react-native-fusebox

This package is private and not expected to become public anytime soon. Consider using [react-devtools-inline](https://github.com/facebook/react/tree/main/packages/react-devtools-inline) or [react-devtools-core](https://github.com/facebook/react/tree/main/packages/react-devtools-core).

## What is Fusebox?
"Fusebox" is the internal codename for the new React Native debugger stack based on Chrome DevTools.
