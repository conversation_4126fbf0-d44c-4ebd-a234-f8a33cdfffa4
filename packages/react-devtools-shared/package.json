{"private": true, "name": "react-devtools-shared", "version": "0.0.0", "scripts": {"update-mock-source-maps": "babel-node --presets=@babel/preset-env,@babel/preset-flow ./src/hooks/__tests__/updateMockSourceMaps.js"}, "devDependencies": {"react-15": "npm:react@^15", "react-dom-15": "npm:react-dom@^15"}, "dependencies": {"@babel/parser": "^7.12.5", "@babel/preset-env": "^7.11.0", "@babel/preset-flow": "^7.10.4", "@babel/runtime": "^7.11.2", "@babel/traverse": "^7.12.5", "@reach/menu-button": "^0.16.1", "@reach/tooltip": "^0.16.0", "clipboard-js": "^0.3.6", "compare-versions": "^5.0.3", "jsc-safe-url": "^0.2.4", "json5": "^2.2.3", "local-storage-fallback": "^4.1.1", "react-virtualized-auto-sizer": "^1.0.23", "react-window": "^1.8.10"}}