.DevTools {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--color-background);
  color: var(--color-text);
  container-name: devtools;
  container-type: inline-size;
}

.TabBar {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  padding: 0 0.5rem;
  background-color: var(--color-background);
  border-top: 1px solid var(--color-border);
  font-family: var(--font-family-sans);
  font-size: var(--font-size-sans-large);
  user-select: none;

  /* Electron drag area */
  -webkit-app-region: drag;
}

.Spacer {
  flex: 1;
}

.TabContent {
  flex: 1 1 100%;
  overflow: auto;
  -webkit-app-region: no-drag;
}

.DevToolsVersion {
  font-size: var(--font-size-sans-normal);
  margin-right: 0.5rem;
}

.DevToolsVersion:before {
  font-size: var(--font-size-sans-large);
  content: 'DevTools ';
}

@media screen and (max-width: 400px) {
  .DevToolsVersion:before {
    content: '';
  }
}

@media screen and (max-width: 300px) {
  .DevToolsVersion {
    display: none;
  }
}

.DevTools, .DevTools * {
  box-sizing: border-box;
  -webkit-font-smoothing: var(--font-smoothing);
}
