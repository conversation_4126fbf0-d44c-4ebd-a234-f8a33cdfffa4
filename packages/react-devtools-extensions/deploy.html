<html>
<head>
  <meta charset="utf8">
  <title>React DevTools pre-release</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Helvetica, Arial,
        sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <h1>
    React DevTools pre-release
  </h1>

  <h3>
    Created on <strong>%date%</strong> from
    <a href="https://github.com/facebook/react/commit/%commit%"><code>%commit%</code></a>
  </h3>

  <p>
    This is a preview build of the <a href="https://github.com/facebook/react">React DevTools extension</a>.
  </p>

  <h2>Installation instructions</h2>
  %installation%
  <p>
    If you already have the React DevTools extension installed, you will need to temporarily disable or remove it in order to install this prerelease build.
  </p>

  <h2>Bug reports</h2>
  <p>
    Please report bugs as <a href="https://github.com/facebook/react/issues/new?labels=Component:%20Developer%20Tools">GitHub issues</a>.
    Please include all of the info required to reproduce the bug (e.g. links, code, instructions).
  </p>
</body>
</html>
