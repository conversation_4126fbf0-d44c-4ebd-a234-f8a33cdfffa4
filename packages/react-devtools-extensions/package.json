{"name": "react-devtools-extensions", "version": "0.0.0", "private": true, "scripts": {"build": "cross-env NODE_ENV=production yarn run build:chrome && yarn run build:firefox && yarn run build:edge", "build:local": "cross-env NODE_ENV=development yarn run build:chrome:local && yarn run build:firefox:local && yarn run build:edge:local", "build:chrome": "cross-env NODE_ENV=production node ./chrome/build", "build:chrome:fb": "cross-env NODE_ENV=production FEATURE_FLAG_TARGET=extension-fb node ./chrome/build --crx", "build:chrome:local": "cross-env NODE_ENV=development node ./chrome/build", "build:chrome:fb:local": "cross-env NODE_ENV=development FEATURE_FLAG_TARGET=extension-fb node ./chrome/build", "build:firefox": "cross-env NODE_ENV=production node ./firefox/build", "build:firefox:local": "cross-env NODE_ENV=development node ./firefox/build", "build:edge": "cross-env NODE_ENV=production node ./edge/build", "build:edge:fb": "cross-env NODE_ENV=production FEATURE_FLAG_TARGET=extension-fb node ./edge/build --crx", "build:edge:local": "cross-env NODE_ENV=development node ./edge/build", "test:chrome": "node ./chrome/test", "test:firefox": "node ./firefox/test", "test:edge": "node ./edge/test", "improve-images": "node ./improveImages.mjs"}, "devDependencies": {"@babel/core": "^7.11.1", "@babel/node": "^7.14.7", "@babel/parser": "^7.14.8", "@babel/plugin-proposal-class-properties": "^7.10.4", "@babel/plugin-transform-flow-strip-types": "^7.10.4", "@babel/plugin-transform-modules-commonjs": "^7.10.4", "@babel/plugin-transform-react-jsx-source": "^7.10.5", "@babel/preset-react": "^7.10.4", "acorn-jsx": "^5.2.0", "archiver": "^3.0.0", "babel-core": "^7.0.0-bridge", "babel-eslint": "^9.0.0", "babel-loader": "^8.0.4", "babel-preset-minify": "^0.5.1", "buffer": "^6.0.3", "chalk": "^4.1.1", "child-process-promise": "^2.2.1", "chrome-launch": "^1.1.4", "crx": "^5.0.0", "css-loader": "^1.0.1", "file-loader": "^6.1.0", "filesize": "^6.0.1", "find": "^0.3.0", "firefox-profile": "^1.0.2", "fs-extra": "^4.0.2", "imagemin": "^8.0.0", "imagemin-gifsicle": "^7.0.0", "imagemin-jpegtran": "^6.0.0", "imagemin-optipng": "^7.0.0", "imagemin-svgo": "^7.0.0", "jest-fetch-mock": "^3.0.3", "node-libs-browser": "0.5.3", "nullthrows": "^1.0.0", "open": "^7.0.2", "os-name": "^3.1.0", "parse-filepath": "^1.0.2", "process": "0.11.10", "raw-loader": "^3.1.0", "rimraf": "^5.0.1", "source-map-js": "^0.6.2", "sourcemap-codec": "^1.4.8", "style-loader": "^0.23.1", "webpack": "^5.82.1", "webpack-cli": "^5.1.1", "webpack-dev-server": "^4.15.0", "workerize-loader": "^2.0.2"}, "dependencies": {"web-ext": "^8"}}