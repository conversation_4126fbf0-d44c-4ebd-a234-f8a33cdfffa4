# The Chrome extension

The source code for this extension has moved to `shells/webextension`.

Modify the source code there and then rebuild this extension by running `node build` from this directory or `yarn run build:extension:chrome` from the root directory.

## Testing in Chrome

You can test a local build of the web extension like so:

 1. Build the extension: `node build`
 1. Follow the on-screen instructions.
