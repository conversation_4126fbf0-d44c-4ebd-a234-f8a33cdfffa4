{"manifest_version": 3, "name": "React Developer Tools", "description": "Adds React debugging tools to the Chrome Developer Tools.", "version": "6.1.5", "version_name": "6.1.5", "minimum_chrome_version": "114", "icons": {"16": "icons/16-production.png", "32": "icons/32-production.png", "48": "icons/48-production.png", "128": "icons/128-production.png"}, "action": {"default_icon": {"16": "icons/16-disabled.png", "32": "icons/32-disabled.png", "48": "icons/48-disabled.png", "128": "icons/128-disabled.png"}, "default_popup": "popups/disabled.html"}, "devtools_page": "main.html", "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'"}, "web_accessible_resources": [{"resources": ["main.html", "panel.html", "build/*.js"], "matches": ["<all_urls>"], "extension_ids": []}], "background": {"service_worker": "build/background.js"}, "permissions": ["scripting", "storage", "tabs"], "optional_permissions": ["clipboardWrite"], "host_permissions": ["<all_urls>"], "content_scripts": [{"matches": ["<all_urls>"], "js": ["build/prepareInjection.js"], "run_at": "document_start"}]}