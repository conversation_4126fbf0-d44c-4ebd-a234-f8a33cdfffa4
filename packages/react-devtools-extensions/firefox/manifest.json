{"manifest_version": 3, "name": "React Developer Tools", "description": "Adds React debugging tools to the Firefox Developer Tools.", "version": "6.1.5", "browser_specific_settings": {"gecko": {"id": "@react-devtools", "strict_min_version": "128.0"}}, "icons": {"16": "icons/16-production.png", "32": "icons/32-production.png", "48": "icons/48-production.png", "128": "icons/128-production.png"}, "action": {"default_icon": {"16": "icons/16-disabled.png", "32": "icons/32-disabled.png", "48": "icons/48-disabled.png", "128": "icons/128-disabled.png"}, "default_popup": "popups/disabled.html"}, "devtools_page": "main.html", "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'"}, "web_accessible_resources": [{"resources": ["main.html", "panel.html", "build/*.js", "build/*.js.map"], "matches": ["<all_urls>"], "extension_ids": []}], "background": {"scripts": ["build/background.js"]}, "permissions": ["scripting", "storage", "tabs", "clipboardWrite"], "host_permissions": ["<all_urls>"], "content_scripts": [{"matches": ["<all_urls>"], "js": ["build/prepareInjection.js"], "run_at": "document_start"}]}