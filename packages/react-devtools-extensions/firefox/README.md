# The Firefox extension

The source code for this extension has moved to `shells/webextension`.

Modify the source code there and then rebuild this extension by running `node build` from this directory or `yarn run build:firefox` from the root directory.

## Testing in Firefox

 1. Build the extension: `node build`
 1. Follow the on-screen instructions.

You can test upcoming releases of Firefox by downloading the Beta or Nightly build from the [Firefox releases](https://www.mozilla.org/en-US/firefox/channel/desktop/) page and then following the on-screen instructions after building.
