/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 */

export {
  COMFORTABLE_LINE_HEIGHT,
  COMPACT_LINE_HEIGHT,
} from 'react-devtools-shared/src/devtools/constants.js';

export const REACT_TOTAL_NUM_LANES = 31;

// Increment this number any time a backwards breaking change is made to the profiler metadata.
export const SCHEDULING_PROFILER_VERSION = 1;

export const SNAPSHOT_MAX_HEIGHT = 60;
