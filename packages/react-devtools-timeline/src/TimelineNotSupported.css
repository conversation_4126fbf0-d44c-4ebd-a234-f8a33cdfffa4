.Column {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 1rem;
}

.Header {
  font-size: var(--font-size-sans-large);
  margin-bottom: 0.5rem;
}

.Paragraph {
  text-align: center;
  margin: 0;
}

.Link {
  color: var(--color-link);
}

.LearnMoreRow {
  margin-top: 1rem;
  color: var(--color-dim);
  font-size: var(--font-size-sans-small);
}

.Code {
  color: var(--color-bridge-version-number);
}

.MetaGKRow {
  background: var(--color-background-hover);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  margin-top: 1rem;
}