.Tooltip {
  position: fixed;
}

.TooltipSection,
.TooltipWarningSection,
.SingleLineTextSection {
  display: block;
  border-radius: 0.125rem;
  padding: 0.25rem;
  user-select: none;
  pointer-events: none;
  background-color: var(--color-tooltip-background);
  box-shadow: 1px 1px 2px var(--color-shadow);
  color: var(--color-tooltip-text);
  font-size: 11px;
}
.TooltipWarningSection {
  margin-top: 0.25rem;
  background-color: var(--color-warning-background);
}
.TooltipSection,
.TooltipWarningSection {
  max-width: 300px;
}
.SingleLineTextSection {
  white-space: nowrap;
}

.Divider {
  height: 1px;
  background-color: #aaa;
  margin: 0.25rem 0;
}

.DetailsGrid {
  display: grid;
  padding-top: 5px;
  grid-gap: 2px 5px;
  grid-template-columns: min-content auto;
}

.DetailsGridLabel {
  color: var(--color-dim);
  text-align: right;
  white-space: nowrap;
}

.DetailsGridLongValue {
  word-break: break-all;
  max-height: 50vh;
  overflow: hidden;
}

.FlamechartStackFrameName {
  word-break: break-word;
}

.ComponentName {
  font-weight: bold;
  word-break: break-word;
  margin-right: 0.25rem;
}

.ReactMeasureLabel {
}

.UserTimingLabel {
  word-break: break-word;
}

.NativeEventName {
  font-weight: bold;
  word-break: break-word;
  margin-right: 0.25rem;
}

.WarningText {
  color: var(--color-warning-text-color);
}

.Image {
  border: 1px solid var(--color-border);
}

.DimText {
  color: var(--color-dim);
}