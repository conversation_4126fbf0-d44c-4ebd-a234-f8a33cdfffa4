.Content {
  width: 100%;
  position: relative;
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}


.ErrorMessage {
  margin: 0.5rem 0;
  color: var(--color-dim);
  font-family: var(--font-family-monospace);
  font-size: var(--font-size-monospace-normal);
}

.Row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  flex-flow: wrap;
}

.EmptyStateContainer {
  text-align: center;
}

.Header {
  font-size: var(--font-size-sans-large);
  margin-bottom: 0.5rem;
}
