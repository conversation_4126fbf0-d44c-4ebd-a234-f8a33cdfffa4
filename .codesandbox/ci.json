{"packages": ["packages/react", "packages/react-dom", "packages/react-server-dom-webpack", "packages/scheduler"], "buildCommand": "download-build-in-codesandbox-ci", "node": "18", "publishDirectory": {"react": "build/oss-experimental/react", "react-dom": "build/oss-experimental/react-dom", "react-server-dom-webpack": "build/oss-experimental/react-server-dom-webpack", "scheduler": "build/oss-experimental/scheduler"}, "sandboxes": ["new"], "silent": true}